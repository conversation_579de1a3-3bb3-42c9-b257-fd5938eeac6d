local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = QBCore.Functions.GetPlayerData()
local cashAmount = 0
local bankAmount = 0

-- تحديث البيانات عند تحميل اللاعب
CreateThread(function()
    while true do
        if PlayerData and PlayerData.money then
            cashAmount = PlayerData.money['cash'] or 0
            bankAmount = PlayerData.money['bank'] or 0
        end
        Wait(1000) -- تحديث كل ثانية
    end
end)

RegisterNetEvent("QBCore:Client:OnPlayerUnload", function()
    PlayerData = {}
    cashAmount = 0
    bankAmount = 0
end)

RegisterNetEvent("QBCore:Player:SetPlayerData", function(val)
    PlayerData = val
    -- تحديث فوري للمتغيرات عند تغيير بيانات اللاعب
    if PlayerData and PlayerData.money then
        cashAmount = PlayerData.money['cash'] or 0
        bankAmount = PlayerData.money['bank'] or 0
    end
end)

-- Money HUD
RegisterNetEvent('hud:client:ShowAccounts', function(type, amount)
    if type == 'bank' then
        -- التأكد من أن المبلغ صحيح
        local displayAmount = amount or bankAmount
        SendNUIMessage({
            action = 'show',
            type = 'bank',
            bank = displayAmount
        })
    end
end)

RegisterNetEvent('hud:client:OnMoneyChange', function(type, amount, isMinus)
    -- الحصول على البيانات المحدثة بشكل متزامن
    QBCore.Functions.GetPlayerData(function(data)
        if data and data.money then
            local currentBankAmount = data.money['bank'] or 0
            bankAmount = currentBankAmount

            -- إرسال البيانات المحدثة
            SendNUIMessage({
                action = 'update',
                bank = currentBankAmount,
                amount = amount or 0,
                minus = isMinus or false,
                plus = not (isMinus or false),
                type = type or 'bank'
            })
        else
            -- في حالة عدم وجود بيانات، استخدم القيم المحفوظة
            SendNUIMessage({
                action = 'update',
                bank = bankAmount,
                amount = amount or 0,
                minus = isMinus or false,
                plus = not (isMinus or false),
                type = type or 'bank'
            })
        end
    end)
end)

-- إضافة event للتحديث اليدوي
RegisterNetEvent('hud:client:UpdateMoney', function()
    QBCore.Functions.GetPlayerData(function(data)
        if data and data.money then
            bankAmount = data.money['bank'] or 0
            cashAmount = data.money['cash'] or 0

            SendNUIMessage({
                action = 'show',
                type = 'bank',
                bank = bankAmount
            })
        end
    end)
end)