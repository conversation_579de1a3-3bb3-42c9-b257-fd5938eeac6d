
const moneyHud = Vue.createApp({
    data() {
        return {
            bank: 0,
            amount: 0,
            plus: false,
            minus: false,
            showBank: false,
            showUpdate: false,
            updateTimeout: null,
            bankTimeout: null,
            showTimeout: null,
        };
    },
    destroyed() {
        window.removeEventListener("message", this.listener);
        if (this.updateTimeout) clearTimeout(this.updateTimeout);
        if (this.bankTimeout) clearTimeout(this.bankTimeout);
        if (this.showTimeout) clearTimeout(this.showTimeout);
    },
    mounted() {
        this.listener = window.addEventListener("message", (event) => {
            try {
                // التأكد من وجود البيانات
                if (!event.data || !event.data.action) {
                    console.warn('Invalid message data received:', event.data);
                    return;
                }

                switch (event.data.action) {
                    case "showconstant":
                        this.showConstant(event.data);
                        break;
                    case "update":
                        this.update(event.data);
                        break;
                    case "show":
                        this.showAccounts(event.data);
                        break;
                    case "hide":
                        this.hideAll();
                        break;
                    default:
                        console.warn('Unknown action:', event.data.action);
                }
            } catch (error) {
                console.error('Error processing message:', error, event.data);
            }
        });
    },
    methods: {

        formatMoney(value) {
            try {
                // التأكد من أن القيمة رقم صحيح
                const numValue = Number(value) || 0;

                // التأكد من أن القيمة ليست NaN أو Infinity
                if (!isFinite(numValue)) {
                    return "$0";
                }

                const formatter = new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: "USD",
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                });
                return formatter.format(numValue);
            } catch (error) {
                console.error('Error formatting money:', error, value);
                return "$0";
            }
        },
        showConstant(data) {
            this.clearAllTimeouts();

            this.showBank = true;
            this.bank = Number(data.bank) || 0;
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;
        },
        update(data) {
            // إلغاء جميع التوقيتات السابقة لتجنب التداخل
            this.clearAllTimeouts();

            // التأكد من صحة البيانات
            this.amount = Number(data.amount) || 0;
            this.bank = Number(data.bank) || 0;
            this.minus = Boolean(data.minus);
            this.plus = Boolean(data.plus);

            if (data.type === "bank") {
                this.showBank = true;
                this.showUpdate = true;

                // إخفاء تحديث المبلغ بعد 2 ثانية
                this.updateTimeout = setTimeout(() => {
                    this.showUpdate = false;
                    this.plus = false;
                    this.minus = false;
                }, 2000);

                // إخفاء عرض البنك بعد 4 ثواني
                this.bankTimeout = setTimeout(() => {
                    this.showBank = false;
                }, 4000);
            }
        },

        // دالة مساعدة لإلغاء جميع التوقيتات
        clearAllTimeouts() {
            if (this.updateTimeout) {
                clearTimeout(this.updateTimeout);
                this.updateTimeout = null;
            }
            if (this.bankTimeout) {
                clearTimeout(this.bankTimeout);
                this.bankTimeout = null;
            }
            if (this.showTimeout) {
                clearTimeout(this.showTimeout);
                this.showTimeout = null;
            }
        },
        showAccounts(data) {
            if (data.type === "bank") {
                // إلغاء التوقيتات السابقة فقط للعرض
                if (this.showTimeout) {
                    clearTimeout(this.showTimeout);
                    this.showTimeout = null;
                }

                this.showBank = true;
                this.bank = Number(data.bank) || 0;

                // إخفاء العرض بعد 4 ثواني إذا لم يكن هناك تحديث
                this.showTimeout = setTimeout(() => {
                    if (!this.showUpdate) {
                        this.showBank = false;
                    }
                }, 4000);
            }
        },
        hideAll() {
            this.clearAllTimeouts();

            this.showBank = false;
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;
        },
    },
}).mount("#money-container");