
const moneyHud = Vue.createApp({
    data() {
        return {
            bank: 0,
            amount: 0,
            plus: false,
            minus: false,
            showBank: false,
            showUpdate: false,
            updateTimeout: null,
            bankTimeout: null,
            showTimeout: null,
        };
    },
    destroyed() {
        window.removeEventListener("message", this.listener);
        if (this.updateTimeout) clearTimeout(this.updateTimeout);
        if (this.bankTimeout) clearTimeout(this.bankTimeout);
        if (this.showTimeout) clearTimeout(this.showTimeout);
    },
    mounted() {
        this.listener = window.addEventListener("message", (event) => {
            switch (event.data.action) {
                case "showconstant":
                    this.showConstant(event.data);
                    break;
                case "update":
                    this.update(event.data);
                    break;
                case "show":
                    this.showAccounts(event.data);
                    break;
                case "hide":
                    this.hideAll();
                    break;
            }
        });
    },
    methods: {

        formatMoney(value) {
            const numValue = Number(value) || 0;
            const formatter = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
                minimumFractionDigits: 0,
            });
            return formatter.format(numValue);
        },
        showConstant(data) {
            if (this.bankTimeout) clearTimeout(this.bankTimeout);
            if (this.showTimeout) clearTimeout(this.showTimeout);

            this.showBank = true;
            this.bank = data.bank;
        },
        update(data) {
            if (this.updateTimeout) clearTimeout(this.updateTimeout);
            if (this.bankTimeout) clearTimeout(this.bankTimeout);

            this.showUpdate = true;
            this.amount = data.amount;
            this.bank = data.bank;
            this.minus = data.minus;
            this.plus = data.plus;

            if (data.type === "bank") {
                this.showBank = true;

                this.updateTimeout = setTimeout(() => {
                    this.showUpdate = false;
                    this.plus = false;
                    this.minus = false;
                }, 1500);

                this.bankTimeout = setTimeout(() => {
                    this.showBank = false;
                }, 3000);
            }
        },
        showAccounts(data) {
            if (data.type === "bank" && !this.showBank) {
                if (this.showTimeout) clearTimeout(this.showTimeout);

                this.showBank = true;
                this.bank = data.bank;

                this.showTimeout = setTimeout(() => {
                    this.showBank = false;
                }, 3500);
            }
        },
        hideAll() {
            if (this.updateTimeout) clearTimeout(this.updateTimeout);
            if (this.bankTimeout) clearTimeout(this.bankTimeout);
            if (this.showTimeout) clearTimeout(this.showTimeout);

            this.showBank = false;
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;
        },
    },
}).mount("#money-container");