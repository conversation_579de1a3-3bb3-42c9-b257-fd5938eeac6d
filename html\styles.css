@import url('https://fonts.cdnfonts.com/css/pricedown');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap');

html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: transparent;
  height: 100%;
}

#main-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

#money-container {
  position: relative;
  margin-right: 20px;
  margin-top: 25vh;
  font-size: clamp(20px, 3.5vw, 28px);
  z-index: 1000;
  font-family: 'Montserrat', 'Pricedown Bl', sans-serif;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.bank-display {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  border: 1px solid #8a2be2;
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  white-space: nowrap;
  width: fit-content;
  max-width: 250px;
  min-height: 32px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  font-size: 0.9em;
}

.change-amount {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  border: 1px solid #8a2be2;
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  white-space: nowrap;
  width: fit-content;
  max-width: 250px;
  min-height: 32px;
  box-sizing: border-box;
  transition: all 0.3s ease, opacity 0.3s ease;
  font-size: 0.9em;
}

#money-change {
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.change-amount {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  border: 1px solid #8a2be2;
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  white-space: nowrap;
  width: fit-content;
  max-width: 250px;
  min-height: 32px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  font-size: 0.9em;
}

.bank-display:hover {
  border-color: #9b4dff;
  transform: translateY(-2px);
}

.bank-icon {
  margin-right: 6px;
  color: #8a2be2;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
}

.bank-amount {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  white-space: nowrap;
}

.currency-text {
  font-family: 'Pricedown Bl', sans-serif;
  text-align: right;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
}

#bank {
  color: #8a2be2;
  letter-spacing: 1px;
}

#plus {
  font-size: 1.1em;
  color: #8a2be2;
  font-weight: bold;
}

#minus {
  font-size: 1.1em;
  color: #ac0000;
  font-weight: bold;
}

.money-amount {
  color: #ffffff;
  font-size: 1em;
}

.money-change-text {
  margin: 0;
  display: flex;
  align-items: center;
  line-height: 1.2;
}

.change-amount p {
  margin: 0;
  display: flex;
  align-items: center;
  line-height: 1.2;
}

@media (max-width: 1280px) {
  #money-container {
    margin-top: 20vh;
    font-size: clamp(18px, 3vw, 26px);
  }

  .bank-display,
  .change-amount {
    min-height: 28px;
    padding: 5px 8px;
  }
}

@media (max-width: 720px) {
  #money-container {
    margin-top: 15vh;
    font-size: clamp(16px, 2.5vw, 22px);
  }

  .bank-display,
  .change-amount {
    min-height: 24px;
    padding: 4px 6px;
    max-width: 200px;
  }

  .bank-icon {
    font-size: 18px;
    margin-right: 4px;
  }
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

