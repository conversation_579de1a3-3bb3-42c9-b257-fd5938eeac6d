local QBCore = exports['qb-core']:GetCoreObject()

-- عرض رصيد البنك
QBCore.Commands.Add('bank', 'Check Bank Balance', {}, false, function(source, _)
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then
        local bankamount = Player.PlayerData.money.bank or 0
        TriggerClientEvent('hud:client:ShowAccounts', source, 'bank', bankamount)
    end
end)

-- إعادة تحديث عرض الأموال (لحل مشاكل التعليق)
QBCore.Commands.Add('refreshmoney', 'Refresh Money Display', {}, false, function(source, _)
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then
        TriggerClientEvent('hud:client:UpdateMoney', source)
    end
end)

-- Event لتحديث عرض الأموال عند تغيير الرصيد
RegisterNetEvent('hud:server:UpdateDisplay', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if Player then
        local bankamount = Player.PlayerData.money.bank or 0
        TriggerClientEvent('hud:client:ShowAccounts', src, 'bank', bankamount)
    end
end)

